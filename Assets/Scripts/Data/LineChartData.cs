using System;
using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

/// <summary>
/// ScriptableObject that stores line chart data configuration
/// Contains series data, axis labels, and chart settings
/// </summary>
[CreateAssetMenu(fileName = "LineChartData", menuName = "Chart Data/Line Chart Data", order = 1)]
public class LineChartData : ScriptableObject
{
    [BoxGroup("Chart Settings"), LabelWidth(120)]
    [Tooltip("Title of the chart")]
    public string chartTitle = "Line Chart";
    
    [BoxGroup("Chart Settings"), LabelWidth(120)]
    [Tooltip("Subtitle of the chart")]
    public string chartSubtitle = "";
    
    [BoxGroup("Chart Settings"), LabelWidth(120)]
    [Tooltip("Width of the chart")]
    public float chartWidth = 800f;
    
    [BoxGroup("Chart Settings"), LabelWidth(120)]
    [Tooltip("Height of the chart")]
    public float chartHeight = 600f;

    [BoxGroup("Axis Configuration"), LabelWidth(120)]
    [Tooltip("Labels for the X-axis")]
    public List<string> xAxisLabels = new List<string>();
    
    [BoxGroup("Axis Configuration"), LabelWidth(120)]
    [Tooltip("Minimum value for Y-axis (auto if not set)")]
    public float yAxisMin = 0f;
    
    [BoxGroup("Axis Configuration"), LabelWidth(120)]
    [Tooltip("Maximum value for Y-axis (auto if not set)")]
    public float yAxisMax = 100f;
    
    [BoxGroup("Axis Configuration"), LabelWidth(120)]
    [Tooltip("Use custom Y-axis range")]
    public bool useCustomYRange = false;

    [BoxGroup("Series Data"), LabelWidth(120)]
    [Tooltip("List of data series for the line chart")]
    public List<LineSeriesData> seriesData = new List<LineSeriesData>();

    [BoxGroup("Chart Appearance"), LabelWidth(120)]
    [Tooltip("Background color of the chart")]
    public Color backgroundColor = Color.white;
    
    [BoxGroup("Chart Appearance"), LabelWidth(120)]
    [Tooltip("Grid line color")]
    public Color gridColor = Color.gray;
    
    [BoxGroup("Chart Appearance"), LabelWidth(120)]
    [Tooltip("Show grid lines")]
    public bool showGrid = true;

    /// <summary>
    /// Data structure for a single line series
    /// </summary>
    [Serializable]
    public class LineSeriesData
    {
        [LabelWidth(100)]
        [Tooltip("Name of the series (appears in legend)")]
        public string seriesName = "Series";
        
        [LabelWidth(100)]
        [Tooltip("Color of the line")]
        public Color lineColor = Color.blue;
        
        [LabelWidth(100)]
        [Tooltip("Width of the line")]
        [Range(1f, 10f)]
        public float lineWidth = 2f;
        
        [LabelWidth(100)]
        [Tooltip("Type of line (Normal, Smooth, Dashed)")]
        public LineType lineType = LineType.Normal;
        
        [LabelWidth(100)]
        [Tooltip("Show data points on the line")]
        public bool showSymbols = true;
        
        [LabelWidth(100)]
        [Tooltip("Size of data point symbols")]
        [Range(2f, 20f)]
        public float symbolSize = 6f;
        
        [LabelWidth(100)]
        [Tooltip("Fill area under the line")]
        public bool showArea = false;
        
        [LabelWidth(100)]
        [ShowIf("showArea")]
        [Tooltip("Area fill color")]
        public Color areaColor = new Color(0.5f, 0.5f, 1f, 0.3f);
        
        [LabelWidth(100)]
        [Tooltip("Y-axis data points for this series")]
        public List<double> dataPoints = new List<double>();
        
        [LabelWidth(100)]
        [Tooltip("Whether this series is visible")]
        public bool isVisible = true;

        /// <summary>
        /// Constructor with default values
        /// </summary>
        public LineSeriesData()
        {
            seriesName = "Series";
            lineColor = Color.blue;
            lineWidth = 2f;
            lineType = LineChartData.LineType.Normal;
            showSymbols = true;
            symbolSize = 6f;
            showArea = false;
            areaColor = new Color(0.5f, 0.5f, 1f, 0.3f);
            dataPoints = new List<double>();
            isVisible = true;
        }

        /// <summary>
        /// Constructor with series name
        /// </summary>
        public LineSeriesData(string name)
        {
            seriesName = name;
            lineColor = Color.blue;
            lineWidth = 2f;
            lineType = LineChartData.LineType.Normal;
            showSymbols = true;
            symbolSize = 6f;
            showArea = false;
            areaColor = new Color(0.5f, 0.5f, 1f, 0.3f);
            dataPoints = new List<double>();
            isVisible = true;
        }
    }

    /// <summary>
    /// Line type enumeration
    /// </summary>
    public enum LineType
    {
        Normal,
        Smooth,
        Dashed
    }

    /// <summary>
    /// Add a new series to the chart data
    /// </summary>
    public LineSeriesData AddSeries(string seriesName)
    {
        var newSeries = new LineSeriesData(seriesName);
        seriesData.Add(newSeries);
        return newSeries;
    }

    /// <summary>
    /// Remove a series by name
    /// </summary>
    public bool RemoveSeries(string seriesName)
    {
        for (int i = 0; i < seriesData.Count; i++)
        {
            if (seriesData[i].seriesName == seriesName)
            {
                seriesData.RemoveAt(i);
                return true;
            }
        }
        return false;
    }

    /// <summary>
    /// Get a series by name
    /// </summary>
    public LineSeriesData GetSeries(string seriesName)
    {
        foreach (var series in seriesData)
        {
            if (series.seriesName == seriesName)
                return series;
        }
        return null;
    }

    /// <summary>
    /// Clear all series data
    /// </summary>
    public void ClearAllSeries()
    {
        seriesData.Clear();
    }

    /// <summary>
    /// Validate the chart data
    /// </summary>
    public bool IsValid()
    {
        if (seriesData.Count == 0) return false;
        
        foreach (var series in seriesData)
        {
            if (series.dataPoints.Count == 0) return false;
        }
        
        return true;
    }
}
