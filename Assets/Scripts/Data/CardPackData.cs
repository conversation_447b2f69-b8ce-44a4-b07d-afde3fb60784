using System.Collections.Generic;
using UnityEngine;
using Sirenix.OdinInspector;

[System.Serializable]
public class CardPackEntry
{
    [HorizontalGroup("Entry", 75)]
    [PreviewField(75)]
    [Tooltip("The card prefab to spawn")]
    public GameObject cardPrefab;
    
    [HorizontalGroup("Entry")]
    [VerticalGroup("Entry/Settings")]
    [LabelWidth(60)]
    [Tooltip("The probability weight for this card (higher = more likely)")]
    [Range(0.1f, 100f)]
    public float weight = 1f;
    
    [VerticalGroup("Entry/Settings")]
    [LabelWidth(60)]
    [Tooltip("Optional: Override the card name for display")]
    public string displayName = "";
    
    [VerticalGroup("Entry/Settings")]
    [ReadOnly]
    [ShowInInspector]
    [LabelWidth(60)]
    [Tooltip("Calculated percentage chance (updated automatically)")]
    public string Percentage => $"{CalculatedPercentage:F1}%";
    
    [System.NonSerialized]
    [HideInInspector]
    public float CalculatedPercentage;
    
    /// <summary>
    /// Gets the display name for this card entry
    /// </summary>
    public string GetDisplayName()
    {
        if (!string.IsNullOrEmpty(displayName))
            return displayName;
        
        if (cardPrefab != null)
            return cardPrefab.name;
            
        return "NULL";
    }
    
    /// <summary>
    /// Validates this card entry
    /// </summary>
    public bool IsValid()
    {
        return cardPrefab != null && weight > 0f;
    }
}

[CreateAssetMenu(fileName = "CardPackData", menuName = "Card System/Card Pack Data", order = 2)]
public class CardPackData : ScriptableObject
{
    [BoxGroup("Pack Information")]
    [Tooltip("Name of this card pack")]
    public string packName = "New Card Pack";
    
    [BoxGroup("Pack Information")]
    [Tooltip("Description of this card pack")]
    [TextArea(2, 4)]
    public string packDescription = "A collection of cards with weighted probabilities.";
    
    [BoxGroup("Pack Configuration")]
    [Tooltip("Default number of cards this pack should generate")]
    [Range(1, 10)]
    public int defaultPackSize = 3;
    
    [BoxGroup("Pack Configuration")]
    [Tooltip("Default price per card in coins")]
    [Range(1, 10)]
    public int defaultPrice = 1;
    
    [BoxGroup("Card Pool")]
    [Tooltip("List of cards that can be spawned with their probability weights")]
    [ListDrawerSettings(
        ShowIndexLabels = true,
        ListElementLabelName = "GetDisplayName",
        DraggableItems = true,
        ShowPaging = true,
        NumberOfItemsPerPage = 10
    )]
    public List<CardPackEntry> cardEntries = new List<CardPackEntry>();
    
    [BoxGroup("Statistics")]
    [ReadOnly]
    [ShowInInspector]
    [Tooltip("Total weight of all valid entries")]
    public float TotalWeight => CalculateTotalWeight();
    
    [BoxGroup("Statistics")]
    [ReadOnly]
    [ShowInInspector]
    [Tooltip("Number of valid card entries")]
    public int ValidEntryCount => GetValidEntryCount();
    
    [BoxGroup("Statistics")]
    [ReadOnly]
    [ShowInInspector]
    [Tooltip("Number of invalid card entries")]
    public int InvalidEntryCount => cardEntries.Count - ValidEntryCount;
    
    private void OnValidate()
    {
        UpdateCalculatedPercentages();
    }
    
    /// <summary>
    /// Calculates the total weight of all valid entries
    /// </summary>
    public float CalculateTotalWeight()
    {
        float total = 0f;
        foreach (var entry in cardEntries)
        {
            if (entry.IsValid())
            {
                total += entry.weight;
            }
        }
        return total;
    }
    
    /// <summary>
    /// Gets the number of valid card entries
    /// </summary>
    public int GetValidEntryCount()
    {
        int count = 0;
        foreach (var entry in cardEntries)
        {
            if (entry.IsValid())
            {
                count++;
            }
        }
        return count;
    }
    
    /// <summary>
    /// Updates the calculated percentages for all entries
    /// </summary>
    public void UpdateCalculatedPercentages()
    {
        float totalWeight = CalculateTotalWeight();
        
        if (totalWeight > 0f)
        {
            foreach (var entry in cardEntries)
            {
                if (entry.IsValid())
                {
                    entry.CalculatedPercentage = (entry.weight / totalWeight) * 100f;
                }
                else
                {
                    entry.CalculatedPercentage = 0f;
                }
            }
        }
        else
        {
            foreach (var entry in cardEntries)
            {
                entry.CalculatedPercentage = 0f;
            }
        }
    }
    
    /// <summary>
    /// Selects a random card based on weighted probabilities
    /// </summary>
    public GameObject SelectRandomCard()
    {
        if (cardEntries.Count == 0)
            return null;
            
        float totalWeight = CalculateTotalWeight();
        if (totalWeight <= 0f)
            return null;
            
        float randomPoint = Random.Range(0f, totalWeight);
        float currentWeight = 0f;
        
        foreach (var entry in cardEntries)
        {
            if (entry.IsValid())
            {
                currentWeight += entry.weight;
                if (randomPoint <= currentWeight)
                {
                    return entry.cardPrefab;
                }
            }
        }
        
        // Fallback - return the last valid entry
        for (int i = cardEntries.Count - 1; i >= 0; i--)
        {
            if (cardEntries[i].IsValid())
            {
                return cardEntries[i].cardPrefab;
            }
        }
        
        return null;
    }
    
    /// <summary>
    /// Validates the card pack data
    /// </summary>
    public List<string> ValidateData()
    {
        List<string> issues = new List<string>();
        
        if (string.IsNullOrEmpty(packName))
        {
            issues.Add("Pack name is empty");
        }
        
        if (cardEntries.Count == 0)
        {
            issues.Add("No card entries defined");
        }
        
        if (GetValidEntryCount() == 0)
        {
            issues.Add("No valid card entries (all entries have null prefabs or zero weight)");
        }
        
        if (CalculateTotalWeight() <= 0f)
        {
            issues.Add("Total weight is zero or negative");
        }
        
        // Check for duplicate prefabs
        HashSet<GameObject> seenPrefabs = new HashSet<GameObject>();
        for (int i = 0; i < cardEntries.Count; i++)
        {
            var entry = cardEntries[i];
            if (entry.cardPrefab != null)
            {
                if (seenPrefabs.Contains(entry.cardPrefab))
                {
                    issues.Add($"Duplicate prefab found: {entry.cardPrefab.name} (entry {i})");
                }
                else
                {
                    seenPrefabs.Add(entry.cardPrefab);
                }
            }
        }
        
        return issues;
    }
    
    [Button("Add New Card Entry")]
    [BoxGroup("Actions")]
    private void AddNewCardEntry()
    {
        cardEntries.Add(new CardPackEntry());
        UpdateCalculatedPercentages();
    }
    
    [Button("Remove Invalid Entries")]
    [BoxGroup("Actions")]
    private void RemoveInvalidEntries()
    {
        cardEntries.RemoveAll(entry => !entry.IsValid());
        UpdateCalculatedPercentages();
    }
    
    [Button("Normalize Weights")]
    [BoxGroup("Actions")]
    [Tooltip("Adjusts all weights so they add up to 100")]
    private void NormalizeWeights()
    {
        float totalWeight = CalculateTotalWeight();
        if (totalWeight > 0f)
        {
            foreach (var entry in cardEntries)
            {
                if (entry.IsValid())
                {
                    entry.weight = (entry.weight / totalWeight) * 100f;
                }
            }
            UpdateCalculatedPercentages();
        }
    }
    
    [Button("Preview Random Selection")]
    [BoxGroup("Debug")]
    private void PreviewRandomSelection()
    {
        if (Application.isPlaying)
        {
            GameObject selected = SelectRandomCard();
            if (selected != null)
            {
                Debug.Log($"Random selection from '{packName}': {selected.name}");
            }
            else
            {
                Debug.Log($"No card selected from '{packName}' (no valid entries)");
            }
        }
        else
        {
            Debug.Log("Preview random selection only works in Play mode");
        }
    }
    
    [Button("Validate Pack Data")]
    [BoxGroup("Debug")]
    private void ValidatePackData()
    {
        var issues = ValidateData();
        if (issues.Count == 0)
        {
            Debug.Log($"Card pack '{packName}' validation passed - no issues found!");
        }
        else
        {
            Debug.LogWarning($"Card pack '{packName}' validation found {issues.Count} issues:\n" + 
                           string.Join("\n", issues));
        }
    }
}
