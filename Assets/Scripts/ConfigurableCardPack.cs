using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using Card;

public class ConfigurableCardPack : MonoBehaviour
{
    [Header("Pack Configuration")]
    [SerializeField] CardPackData packData;
    [SerializeField] int packSize = 3;
    [SerializeField] float circleRadius = 2.5f;

    [Header("UI Configuration")]
    [SerializeField] string packTitle = "Card Pack";
    [SerializeField] string packDescription = "Open this Pack to get Cards!";

    [Header("Remaining Cards UI")]
    [SerializeField] SpriteRenderer remainDisplay;
    [SerializeField] List<Sprite> remainSprites = new List<Sprite>();

    // Private fields
    private TextMeshProUGUI titleText, detailedText;
    private Zoom gameCam;
    private int cardsOpened;
    private int remainingCards;

    private void Awake()
    {
        gameCam = GameObject.Find("Main Camera")?.GetComponent<Zoom>();
        titleText = GameObject.Find("TitleText (TMP)")?.GetComponent<TextMeshProUGUI>();
        detailedText = GameObject.Find("DetailedText (TMP)")?.GetComponent<TextMeshProUGUI>();
    }

    void Start()
    {
        // Initialize pack configuration
        if (packData != null)
        {
            packSize = packData.defaultPackSize;
            packTitle = packData.packName;
            packDescription = packData.packDescription;
        }

        cardsOpened = 0;
        remainingCards = packSize;

        // Update remaining cards display
        UpdateRemainingDisplay();

        // Validate pack data
        if (packData == null)
        {
            Debug.LogWarning($"ConfigurableCardPack '{gameObject.name}' has no CardPackData assigned!");
        }
        else if (packData.ValidEntryCount == 0)
        {
            Debug.LogWarning($"ConfigurableCardPack '{gameObject.name}' has no valid card entries in its pack data!");
        }
    }

    void Update()
    {
        // No update logic needed - pack opening is handled by mouse events
    }
    private void OnMouseDrag()
    {
        if (gameCam != null)
            gameCam.enableDrag = false;
        Vector3 mousePos = Camera.main.ScreenToWorldPoint(Input.mousePosition);
        transform.position = new Vector3(mousePos.x, mousePos.y, 0);
    }

    private void OnMouseExit()
    {
        if (titleText != null)
            titleText.text = "";
        if (detailedText != null)
            detailedText.text = "";
    }

    private void OnMouseUp()
    {
        if (gameCam != null)
            gameCam.enableDrag = true;
    }
    private void OnMouseOver()
    {
        if (titleText != null)
            titleText.text = packTitle;
        if (detailedText != null)
            detailedText.text = packDescription;

        if (Input.GetMouseButtonUp(0))
        {
            OpenCard();
            SoundManager.instance?.PlayOpenPack();
        }
    }
    public void OpenCard()
    {
        if (cardsOpened >= packSize || packData == null)
            return;

        GameObject cardToSpawn = packData.SelectRandomCard();
        if (cardToSpawn != null)
        {
            Vector3 spawnPosition = CalculateSpawnPosition();
            GameObject newCard = Instantiate(cardToSpawn, spawnPosition, Quaternion.identity);

            GameCard gameCard = newCard.GetComponent<GameCard>();
            if (gameCard != null)
            {
                gameCard.startPos = spawnPosition;
            }

            cardsOpened++;
            remainingCards--;

            // Update remaining cards display
            UpdateRemainingDisplay();

            // Check if pack is fully opened
            if (cardsOpened >= packSize)
            {
                if (titleText != null) titleText.text = "";
                if (detailedText != null) detailedText.text = "";
                Destroy(gameObject);
            }
        }
    }
    private Vector3 CalculateSpawnPosition()
    {
        if (packSize <= 1)
        {
            return transform.position;
        }

        Vector3 circleCenter = transform.position;
        float angle = cardsOpened * Mathf.PI * 2f / packSize;
        float x = circleCenter.x + circleRadius * Mathf.Cos(angle);
        float y = circleCenter.y + circleRadius * Mathf.Sin(angle);
        return new Vector3(x, y, 0f);
    }

    private void UpdateRemainingDisplay()
    {
        if (remainDisplay != null && remainSprites.Count > 0)
        {
            int spriteIndex = Mathf.Clamp(remainingCards, 0, remainSprites.Count - 1);
            if (spriteIndex < remainSprites.Count)
            {
                remainDisplay.sprite = remainSprites[spriteIndex];
            }
        }
    }
}
