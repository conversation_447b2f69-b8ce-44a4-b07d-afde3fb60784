using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using Card;
using Sirenix.OdinInspector;

[System.Serializable]
public class WeightedCardItem
{
    [HorizontalGroup("Card Item")]
    [PreviewField(50)]
    [Tooltip("The card prefab to spawn")]
    public GameObject cardPrefab;
    
    [HorizontalGroup("Card Item")]
    [LabelWidth(60)]
    [Tooltip("The probability weight for this card (higher = more likely)")]
    public float weight = 1f;
    
    [HorizontalGroup("Card Item")]
    [LabelWidth(80)]
    [Tooltip("Optional: Override the card name for display")]
    public string displayName = "";
}

public class CustomizablePack : MonoBehaviour
{
    [BoxGroup("Pack Configuration")]
    [Tooltip("Number of cards to generate when pack is opened")]
    [Range(1, 10)]
    public int packSize = 3;
    
    [BoxGroup("Pack Configuration")]
    [Tooltip("Price in coins required to open this pack")]
    public int price = 3;
    
    [BoxGroup("Pack Configuration")]
    [Tooltip("Radius for arranging spawned cards in a circle")]
    public float spawnRadius = 2.5f;
    
    [BoxGroup("Pack Configuration")]
    [Tooltip("Whether to arrange cards in a circle (true) or random positions (false)")]
    public bool arrangeInCircle = true;
    
    [BoxGroup("Pack Configuration")]
    [Tooltip("Random position spread when not arranging in circle")]
    public Vector2 randomSpread = new Vector2(3f, 3f);
    
    [BoxGroup("Card Pool")]
    [Tooltip("List of cards that can be spawned with their probability weights")]
    public List<WeightedCardItem> cardPool = new List<WeightedCardItem>();
    
    [BoxGroup("Special Cards")]
    [Tooltip("Special cards that can replace normal cards (like idea cards)")]
    public List<GameObject> specialCards = new List<GameObject>();
    
    [BoxGroup("Special Cards")]
    [ShowIf("@specialCards.Count > 0")]
    [Tooltip("Chance (0-1) that a special card will replace a normal card")]
    [Range(0f, 1f)]
    public float specialCardChance = 0.1f;
    
    [BoxGroup("UI")]
    [Tooltip("Pack title displayed when hovering")]
    public string packTitle = "Card Pack";
    
    [BoxGroup("UI")]
    [Tooltip("Pack description displayed when hovering")]
    public string packDescription = "Open this pack to get cards!";
    
    [BoxGroup("Runtime")]
    [ReadOnly]
    [Tooltip("Current coins needed to open pack")]
    public int coinNeed;
    
    [BoxGroup("Runtime")]
    [ReadOnly]
    [Tooltip("Number of cards opened so far")]
    public int cardsOpened = 0;
    
    // Private fields
    private Collider2D cld;
    private TextMeshProUGUI titleText;
    private TextMeshProUGUI detailedText;
    private Zoom gameCam;
    private List<GameObject> availableSpecialCards;
    
    private void Awake()
    {
        cld = GetComponent<Collider2D>();
        gameCam = GameObject.Find("Main Camera")?.GetComponent<Zoom>();
        
        // Find UI text components
        titleText = GameObject.Find("TitleText (TMP)")?.GetComponent<TextMeshProUGUI>();
        detailedText = GameObject.Find("DetailedText (TMP)")?.GetComponent<TextMeshProUGUI>();
        
        // Initialize available special cards list
        availableSpecialCards = new List<GameObject>(specialCards);
    }
    
    private void Start()
    {
        coinNeed = price;
        cardsOpened = 0;
        
        // Validate card pool
        if (cardPool.Count == 0)
        {
            Debug.LogWarning($"CustomizablePack '{gameObject.name}' has no cards in the card pool!");
        }
    }
    
    private void Update()
    {
        // Handle mouse input for dragging
        if (Input.GetMouseButtonDown(0))
        {
            cld.enabled = false;
        }
        if (Input.GetMouseButtonUp(0))
        {
            cld.enabled = true;
        }
        
        // Check if pack should be opened
        if (coinNeed <= 0 && cardsOpened < packSize)
        {
            OpenPack();
        }
    }
    
    private void OnTriggerStay2D(Collider2D collision)
    {
        if (collision.CompareTag("Coin"))
        {
            coinNeed--;
            SoundManager.instance?.PlayBuyPack();
            collision.gameObject.GetComponent<GameCard>()?.CardDestroy();
        }
        else
        {
            // Push away non-coin objects
            collision.gameObject.GetComponent<Rigidbody2D>()?.AddForce(Vector3.down * 15f);
        }
    }
    
    private void OnMouseOver()
    {
        if (titleText != null)
            titleText.text = packTitle;
        if (detailedText != null)
            detailedText.text = packDescription;
            
        if (Input.GetMouseButtonUp(0))
        {
            SoundManager.instance?.PlayOpenPack();
        }
    }
    
    private void OnMouseExit()
    {
        if (titleText != null)
            titleText.text = "";
        if (detailedText != null)
            detailedText.text = "";
    }
    
    private void OnMouseDrag()
    {
        if (gameCam != null)
            gameCam.enableDrag = false;
            
        Vector3 mousePos = Camera.main.ScreenToWorldPoint(Input.mousePosition);
        transform.position = new Vector3(mousePos.x, mousePos.y, 0);
    }
    
    private void OnMouseUp()
    {
        if (gameCam != null)
            gameCam.enableDrag = true;
    }
    
    public void OpenPack()
    {
        if (cardsOpened >= packSize || cardPool.Count == 0)
            return;
            
        GameObject cardToSpawn = SelectRandomCard();
        if (cardToSpawn != null)
        {
            Vector3 spawnPosition = CalculateSpawnPosition();
            GameObject newCard = Instantiate(cardToSpawn, spawnPosition, Quaternion.identity);
            
            // Set up the card's starting position for animation
            GameCard gameCard = newCard.GetComponent<GameCard>();
            if (gameCard != null)
            {
                gameCard.startPos = spawnPosition;
            }
            
            cardsOpened++;
            
            // Reset coin requirement for next card
            if (cardsOpened < packSize)
            {
                coinNeed = price;
            }
            else
            {
                // Pack is fully opened, destroy it
                if (titleText != null) titleText.text = "";
                if (detailedText != null) detailedText.text = "";
                Destroy(gameObject);
            }
        }
    }
    
    private GameObject SelectRandomCard()
    {
        // Check if we should spawn a special card
        if (availableSpecialCards.Count > 0 && Random.Range(0f, 1f) < specialCardChance)
        {
            int specialIndex = Random.Range(0, availableSpecialCards.Count);
            GameObject specialCard = availableSpecialCards[specialIndex];
            availableSpecialCards.RemoveAt(specialIndex); // Remove so it can't be selected again
            
            // Add to ideas found if GameManager exists
            if (GameManager.instance != null)
            {
                string cardName = specialCard.name;
                if (cardName.EndsWith("(Clone)"))
                    cardName = cardName.Substring(0, cardName.Length - 7);
                GameManager.instance.ideasFound.Add(cardName);
            }
            
            return specialCard;
        }
        
        // Use weighted random selection for normal cards
        return SelectWeightedRandomCard();
    }
    
    private GameObject SelectWeightedRandomCard()
    {
        if (cardPool.Count == 0)
            return null;
            
        // Calculate total weight
        float totalWeight = 0f;
        foreach (var item in cardPool)
        {
            totalWeight += item.weight;
        }
        
        if (totalWeight <= 0f)
        {
            Debug.LogWarning($"CustomizablePack '{gameObject.name}' has no positive weights in card pool!");
            return cardPool[0].cardPrefab; // Return first card as fallback
        }
        
        // Generate random point
        float randomPoint = Random.Range(0f, totalWeight);
        
        // Find which card corresponds to the random point
        float currentWeight = 0f;
        foreach (var item in cardPool)
        {
            currentWeight += item.weight;
            if (randomPoint <= currentWeight)
            {
                return item.cardPrefab;
            }
        }
        
        // Fallback (should never reach here)
        return cardPool[cardPool.Count - 1].cardPrefab;
    }
    
    private Vector3 CalculateSpawnPosition()
    {
        if (arrangeInCircle && packSize > 1)
        {
            // Arrange in circle
            float angle = cardsOpened * Mathf.PI * 2f / packSize;
            float x = transform.position.x + spawnRadius * Mathf.Cos(angle);
            float y = transform.position.y + spawnRadius * Mathf.Sin(angle);
            return new Vector3(x, y, 0f);
        }
        else
        {
            // Random position around pack
            float randomX = Random.Range(-randomSpread.x, randomSpread.x);
            float randomY = Random.Range(-randomSpread.y, randomSpread.y);
            return transform.position + new Vector3(randomX, randomY, 0f);
        }
    }
    
    [Button("Preview Card Weights")]
    [BoxGroup("Debug")]
    private void PreviewCardWeights()
    {
        if (cardPool.Count == 0)
        {
            Debug.Log("No cards in pool to preview.");
            return;
        }
        
        float totalWeight = 0f;
        foreach (var item in cardPool)
        {
            totalWeight += item.weight;
        }
        
        Debug.Log($"=== Card Weight Preview for '{gameObject.name}' ===");
        foreach (var item in cardPool)
        {
            float percentage = (item.weight / totalWeight) * 100f;
            string cardName = !string.IsNullOrEmpty(item.displayName) ? item.displayName : 
                             (item.cardPrefab != null ? item.cardPrefab.name : "NULL");
            Debug.Log($"{cardName}: {item.weight} weight ({percentage:F1}% chance)");
        }
        Debug.Log($"Total Weight: {totalWeight}");
    }
}
