using System.Collections.Generic;
using System.IO;
using Sirenix.OdinInspector;
using Sirenix.OdinInspector.Editor;
using Sirenix.Utilities.Editor;
using UnityEditor;
using UnityEngine;

public class CardPackDataEditor : OdinMenuEditorWindow
{
    [MenuItem("Tools/Card Pack Data Editor")]
    private static void OpenWindow()
    {
        GetWindow<CardPackDataEditor>().Show();
    }

    private CreateNewCardPackData _createNewCardPackData;

    protected override void OnEnable()
    {
        base.OnEnable();
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();
        if (_createNewCardPackData != null)
        {
            DestroyImmediate(_createNewCardPackData.cardPackData);
        }
    }

    protected override OdinMenuTree BuildMenuTree()
    {
        var tree = new OdinMenuTree();
        _createNewCardPackData = new CreateNewCardPackData();
        tree.Add("Create New", _createNewCardPackData);
        tree.AddAllAssetsAtPath("Card Pack Data", "Assets/Data", typeof(CardPackData));
        return tree;
    }

    protected override void OnBeginDrawEditors()
    {
        OdinMenuTreeSelection selected = this.MenuTree.Selection;

        SirenixEditorGUI.BeginHorizontalToolbar();
        {
            // Delete button
            if (SirenixEditorGUI.ToolbarButton("Delete Current"))
            {
                CardPackData data = selected.SelectedValue as CardPackData;
                if (data != null)
                {
                    bool confirm = EditorUtility.DisplayDialog("Delete Card Pack Data",
                        $"Are you sure you want to delete '{data.packName}'?",
                        "Delete", "Cancel");
                    
                    if (confirm)
                    {
                        string path = AssetDatabase.GetAssetPath(data);
                        AssetDatabase.DeleteAsset(path);
                        AssetDatabase.SaveAssets();
                    }
                }
            }

            GUILayout.Space(10);

            // Duplicate button
            GUI.enabled = selected.SelectedValue is CardPackData;
            if (SirenixEditorGUI.ToolbarButton("Duplicate"))
            {
                CardPackData data = selected.SelectedValue as CardPackData;
                if (data != null)
                {
                    DuplicateCardPackData(data);
                }
            }
            GUI.enabled = true;

            GUILayout.Space(10);

            // Validate button
            GUI.enabled = selected.SelectedValue is CardPackData;
            if (SirenixEditorGUI.ToolbarButton("Validate"))
            {
                CardPackData data = selected.SelectedValue as CardPackData;
                if (data != null)
                {
                    ValidateCardPackData(data);
                }
            }
            GUI.enabled = true;

            GUILayout.FlexibleSpace();

            // Import from existing pack button
            if (SirenixEditorGUI.ToolbarButton("Import from HumbleBeginnings"))
            {
                ImportFromHumbleBeginnings();
            }

            GUILayout.Space(5);

            // Help button
            if (SirenixEditorGUI.ToolbarButton("Help"))
            {
                ShowHelpDialog();
            }
        }
        SirenixEditorGUI.EndHorizontalToolbar();

        // Show validation status for selected pack
        CardPackData selectedPack = selected.SelectedValue as CardPackData;
        if (selectedPack != null)
        {
            var issues = selectedPack.ValidateData();
            if (issues.Count > 0)
            {
                SirenixEditorGUI.ErrorMessageBox($"Validation Issues ({issues.Count}):\n" + 
                                               string.Join("\n", issues));
            }
            else if (selectedPack.cardEntries.Count > 0)
            {
                SirenixEditorGUI.InfoMessageBox($"Pack '{selectedPack.packName}' is valid! " +
                                              $"Contains {selectedPack.ValidEntryCount} cards with total weight {selectedPack.TotalWeight:F1}");
            }
        }
    }

    private void DuplicateCardPackData(CardPackData original)
    {
        // Create a copy
        CardPackData copy = Instantiate(original);
        copy.packName = original.packName + " Copy";
        
        // Create unique filename
        string originalPath = AssetDatabase.GetAssetPath(original);
        string directory = Path.GetDirectoryName(originalPath);
        string filename = Path.GetFileNameWithoutExtension(originalPath);
        string extension = Path.GetExtension(originalPath);
        
        string newPath = Path.Combine(directory, filename + "_Copy" + extension);
        int counter = 1;
        while (File.Exists(newPath))
        {
            newPath = Path.Combine(directory, filename + "_Copy" + counter + extension);
            counter++;
        }
        
        AssetDatabase.CreateAsset(copy, newPath);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();
        
        EditorUtility.DisplayDialog("Duplicated", $"Created copy at: {newPath}", "OK");
    }

    private void ValidateCardPackData(CardPackData data)
    {
        var issues = data.ValidateData();
        if (issues.Count == 0)
        {
            EditorUtility.DisplayDialog("Validation Passed", 
                $"Card pack '{data.packName}' is valid!\n\n" +
                $"Valid entries: {data.ValidEntryCount}\n" +
                $"Total weight: {data.TotalWeight:F1}", "OK");
        }
        else
        {
            EditorUtility.DisplayDialog("Validation Failed", 
                $"Card pack '{data.packName}' has {issues.Count} issues:\n\n" +
                string.Join("\n", issues), "OK");
        }
    }

    private void ImportFromHumbleBeginnings()
    {
        // Find HumbleBeginnings objects in the scene
        HumbleBeginnings[] humbleBeginnings = FindObjectsOfType<HumbleBeginnings>();
        
        if (humbleBeginnings.Length == 0)
        {
            EditorUtility.DisplayDialog("No HumbleBeginnings Found", 
                "No HumbleBeginnings components found in the current scene.", "OK");
            return;
        }

        // Let user select which one to import from if multiple exist
        HumbleBeginnings selected = humbleBeginnings[0];
        if (humbleBeginnings.Length > 1)
        {
            string[] options = new string[humbleBeginnings.Length];
            for (int i = 0; i < humbleBeginnings.Length; i++)
            {
                options[i] = humbleBeginnings[i].gameObject.name;
            }
            
            int choice = EditorUtility.DisplayDialogComplex("Multiple HumbleBeginnings Found",
                "Multiple HumbleBeginnings components found. Which one would you like to import from?",
                options[0], "Cancel", humbleBeginnings.Length > 1 ? options[1] : "");
                
            if (choice == 1) return; // Cancel
            if (choice == 2 && humbleBeginnings.Length > 1) selected = humbleBeginnings[1];
        }

        // Import the data
        ImportHumbleBeginngsData(selected);
    }

    private void ImportHumbleBeginngsData(HumbleBeginnings humbleBeginnings)
    {
        // Access private fields using reflection
        var listField = typeof(HumbleBeginnings).GetField("list", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        var percentField = typeof(HumbleBeginnings).GetField("percent", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (listField == null || percentField == null)
        {
            EditorUtility.DisplayDialog("Import Failed", 
                "Could not access HumbleBeginnings data fields. The class structure may have changed.", "OK");
            return;
        }

        var cardList = listField.GetValue(humbleBeginnings) as List<GameObject>;
        var percentList = percentField.GetValue(humbleBeginnings) as List<float>;

        if (cardList == null || percentList == null)
        {
            EditorUtility.DisplayDialog("Import Failed", 
                "HumbleBeginnings data fields are null.", "OK");
            return;
        }

        if (cardList.Count != percentList.Count)
        {
            EditorUtility.DisplayDialog("Import Failed", 
                "Card list and percent list have different lengths.", "OK");
            return;
        }

        // Create new CardPackData
        CardPackData newPackData = ScriptableObject.CreateInstance<CardPackData>();
        newPackData.packName = humbleBeginnings.gameObject.name + " Pack";
        newPackData.packDescription = "Imported from HumbleBeginnings component";
        newPackData.defaultPackSize = 3; // HumbleBeginnings always spawns 3 cards
        newPackData.defaultPrice = 1;

        // Import card entries
        for (int i = 0; i < cardList.Count; i++)
        {
            if (cardList[i] != null)
            {
                CardPackEntry entry = new CardPackEntry();
                entry.cardPrefab = cardList[i];
                entry.weight = percentList[i];
                entry.displayName = cardList[i].name;
                newPackData.cardEntries.Add(entry);
            }
        }

        newPackData.UpdateCalculatedPercentages();

        // Save the asset
        string path = $"Assets/Data/{newPackData.packName}.asset";
        int counter = 1;
        while (File.Exists(path))
        {
            path = $"Assets/Data/{newPackData.packName}_{counter}.asset";
            counter++;
        }

        AssetDatabase.CreateAsset(newPackData, path);
        AssetDatabase.SaveAssets();
        AssetDatabase.Refresh();

        EditorUtility.DisplayDialog("Import Successful", 
            $"Successfully imported {newPackData.cardEntries.Count} cards from '{humbleBeginnings.gameObject.name}'\n\n" +
            $"Created: {path}", "OK");
    }

    private void ShowHelpDialog()
    {
        EditorUtility.DisplayDialog("Card Pack Data Editor Help",
            "Card Pack Data Editor allows you to create and manage card pack configurations.\n\n" +
            "Features:\n" +
            "• Create new card pack data assets\n" +
            "• Configure card prefabs with probability weights\n" +
            "• Preview calculated percentages\n" +
            "• Validate pack data for errors\n" +
            "• Import from existing HumbleBeginnings components\n" +
            "• Duplicate existing packs\n\n" +
            "Usage:\n" +
            "1. Create a new pack or select an existing one\n" +
            "2. Add card entries with prefabs and weights\n" +
            "3. Use the validation tools to check for issues\n" +
            "4. Use the pack data in ConfigurableCardPack components", "OK");
    }

    public class CreateNewCardPackData
    {
        public CreateNewCardPackData()
        {
            cardPackData = ScriptableObject.CreateInstance<CardPackData>();
            cardPackData.name = "New Card Pack Data";
        }

        [InlineEditor(ObjectFieldMode = InlineEditorObjectFieldModes.Hidden)]
        public CardPackData cardPackData;

        [Button("Create New Card Pack Data")]
        private void CreateCardPackData()
        {
            if (string.IsNullOrEmpty(cardPackData.packName))
            {
                EditorUtility.DisplayDialog("Invalid Name", "Please enter a pack name before creating.", "OK");
                return;
            }

            string filename = System.Text.RegularExpressions.Regex.Replace(cardPackData.packName, @"[^\w\d]", "_");
            string path = $"Assets/Data/{filename}.asset";
            
            // Check if file already exists
            int counter = 1;
            while (File.Exists(path))
            {
                path = $"Assets/Data/{filename}_{counter}.asset";
                counter++;
            }

            AssetDatabase.CreateAsset(cardPackData, path);
            AssetDatabase.SaveAssets();

            cardPackData = ScriptableObject.CreateInstance<CardPackData>();
            cardPackData.packName = "New Card Pack Data";
        }
    }
}
