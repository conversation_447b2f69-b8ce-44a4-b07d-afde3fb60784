using System.Collections.Generic;
using System.IO;
using UnityEngine;
using UnityEditor;
using Sirenix.OdinInspector.Editor;
using Sirenix.OdinInspector;
using Sirenix.Utilities.Editor;
using Sirenix.Utilities;

/// <summary>
/// Custom editor window for LineChartData assets, similar to CardDataEditor
/// Provides a comprehensive interface for creating, editing, and managing line chart data
/// </summary>
public class LineChartDataEditor : OdinMenuEditorWindow
{
    // EditorPrefs keys for persistent settings
    private const string PREF_EXPORT_FOLDER = "LineChartDataEditor_ExportFolder";
    private const string PREF_EXPORT_FORMAT = "LineChartDataEditor_ExportFormat";
    private const string PREF_AUTO_VALIDATE = "LineChartDataEditor_AutoValidate";

    // Settings (static to persist across window instances)
    private static string exportFolder = "Assets/Exports/ChartData";
    private static ExportFormat exportFormat = ExportFormat.JSON;
    private static bool autoValidate = true;

    // Create new chart data instance
    private CreateNewLineChartData _createNewChartData;

    public enum ExportFormat
    {
        JSON,
        CSV,
        XML
    }

    [MenuItem("Tools/LineChartDataEditor")]
    private static void OpenWindow()
    {
        GetWindow<LineChartDataEditor>().Show();
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        LoadSettings();
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        SaveSettings();
    }

    public void LoadSettings()
    {
        // Load settings from EditorPrefs
        exportFolder = EditorPrefs.GetString(PREF_EXPORT_FOLDER, "Assets/Exports/ChartData");
        exportFormat = (ExportFormat)EditorPrefs.GetInt(PREF_EXPORT_FORMAT, (int)ExportFormat.JSON);
        autoValidate = EditorPrefs.GetBool(PREF_AUTO_VALIDATE, true);
    }

    public void SaveSettings()
    {
        // Save settings to EditorPrefs
        EditorPrefs.SetString(PREF_EXPORT_FOLDER, exportFolder);
        EditorPrefs.SetInt(PREF_EXPORT_FORMAT, (int)exportFormat);
        EditorPrefs.SetBool(PREF_AUTO_VALIDATE, autoValidate);
    }

    protected override OdinMenuTree BuildMenuTree()
    {
        var tree = new OdinMenuTree();
        _createNewChartData = new CreateNewLineChartData();
        tree.Add("Create New", _createNewChartData);
        tree.AddAllAssetsAtPath("Chart Data List", "Assets/Data", typeof(LineChartData));
        return tree;
    }

    protected override void OnBeginDrawEditors()
    {
        OdinMenuTreeSelection selected = this.MenuTree.Selection;

        SirenixEditorGUI.BeginHorizontalToolbar();
        {
            // Delete button
            if (SirenixEditorGUI.ToolbarButton("Delete Current"))
            {
                LineChartData data = selected.SelectedValue as LineChartData;
                if (data != null)
                {
                    if (EditorUtility.DisplayDialog("Delete Chart Data", 
                        $"Are you sure you want to delete '{data.name}'?", "Delete", "Cancel"))
                    {
                        string path = AssetDatabase.GetAssetPath(data);
                        AssetDatabase.DeleteAsset(path);
                        AssetDatabase.SaveAssets();
                    }
                }
            }

            GUILayout.Space(10);

            // Validate button
            GUI.enabled = selected.SelectedValue is LineChartData;
            if (SirenixEditorGUI.ToolbarButton("Validate"))
            {
                LineChartData data = selected.SelectedValue as LineChartData;
                if (data != null)
                {
                    ValidateChartData(data);
                }
            }
            GUI.enabled = true;

            GUILayout.Space(10);

            // Export button
            GUI.enabled = selected.SelectedValue is LineChartData;
            if (SirenixEditorGUI.ToolbarButton("Export"))
            {
                LineChartData data = selected.SelectedValue as LineChartData;
                if (data != null)
                {
                    ExportChartData(data);
                }
            }
            GUI.enabled = true;

            GUILayout.Space(10);

            // Export All button
            if (SirenixEditorGUI.ToolbarButton("Export All"))
            {
                ExportAllChartData();
            }

            GUILayout.FlexibleSpace();

            // Preview button (placeholder for future chart rendering integration)
            GUI.enabled = selected.SelectedValue is LineChartData;
            if (SirenixEditorGUI.ToolbarButton("Preview"))
            {
                LineChartData data = selected.SelectedValue as LineChartData;
                if (data != null)
                {
                    PreviewChart(data);
                }
            }
            GUI.enabled = true;

            GUILayout.Space(5);

            // Settings button
            if (SirenixEditorGUI.ToolbarButton("Settings"))
            {
                LineChartSettingsWindow.ShowWindow();
            }
        }
        SirenixEditorGUI.EndHorizontalToolbar();

        // Show validation status for selected chart
        if (selected.SelectedValue is LineChartData selectedChart)
        {
            if (autoValidate)
            {
                ShowValidationStatus(selectedChart);
            }
        }
    }

    #region Validation Methods

    private void ValidateChartData(LineChartData chartData)
    {
        var issues = GetValidationIssues(chartData);
        
        if (issues.Count == 0)
        {
            EditorUtility.DisplayDialog("Validation Success", 
                $"Chart '{chartData.chartTitle}' is valid and ready to use!", "OK");
        }
        else
        {
            string issueText = string.Join("\n• ", issues);
            EditorUtility.DisplayDialog("Validation Issues", 
                $"Found {issues.Count} issue(s) in '{chartData.chartTitle}':\n\n• {issueText}", "OK");
        }
    }

    private List<string> GetValidationIssues(LineChartData chartData)
    {
        var issues = new List<string>();

        // Check basic chart settings
        if (string.IsNullOrEmpty(chartData.chartTitle))
            issues.Add("Chart title is empty");

        if (chartData.chartWidth <= 0)
            issues.Add("Chart width must be greater than 0");

        if (chartData.chartHeight <= 0)
            issues.Add("Chart height must be greater than 0");

        // Check series data
        if (chartData.seriesData.Count == 0)
            issues.Add("No data series defined");

        for (int i = 0; i < chartData.seriesData.Count; i++)
        {
            var series = chartData.seriesData[i];
            if (string.IsNullOrEmpty(series.seriesName))
                issues.Add($"Series {i + 1} has no name");

            if (series.dataPoints.Count == 0)
                issues.Add($"Series '{series.seriesName}' has no data points");

            if (series.lineWidth <= 0)
                issues.Add($"Series '{series.seriesName}' has invalid line width");
        }

        // Check X-axis labels vs data points consistency
        if (chartData.xAxisLabels.Count > 0 && chartData.seriesData.Count > 0)
        {
            int expectedDataPoints = chartData.xAxisLabels.Count;
            foreach (var series in chartData.seriesData)
            {
                if (series.dataPoints.Count != expectedDataPoints)
                {
                    issues.Add($"Series '{series.seriesName}' has {series.dataPoints.Count} data points but {expectedDataPoints} X-axis labels");
                }
            }
        }

        // Check Y-axis range
        if (chartData.useCustomYRange && chartData.yAxisMin >= chartData.yAxisMax)
            issues.Add("Y-axis minimum must be less than maximum");

        return issues;
    }

    private void ShowValidationStatus(LineChartData chartData)
    {
        var issues = GetValidationIssues(chartData);
        
        if (issues.Count == 0)
        {
            SirenixEditorGUI.InfoMessageBox($"✓ Chart '{chartData.chartTitle}' is valid");
        }
        else
        {
            string issueText = string.Join(", ", issues);
            SirenixEditorGUI.ErrorMessageBox($"⚠ {issues.Count} validation issue(s): {issueText}");
        }
    }

    #endregion

    #region Export Methods

    private void ExportChartData(LineChartData chartData)
    {
        // Create export directory if it doesn't exist
        if (!Directory.Exists(exportFolder))
        {
            Directory.CreateDirectory(exportFolder);
        }

        try
        {
            string fileName = $"{chartData.name}_{System.DateTime.Now:yyyyMMdd_HHmmss}";
            string filePath = "";

            switch (exportFormat)
            {
                case ExportFormat.JSON:
                    filePath = Path.Combine(exportFolder, $"{fileName}.json");
                    ExportToJSON(chartData, filePath);
                    break;
                case ExportFormat.CSV:
                    filePath = Path.Combine(exportFolder, $"{fileName}.csv");
                    ExportToCSV(chartData, filePath);
                    break;
                case ExportFormat.XML:
                    filePath = Path.Combine(exportFolder, $"{fileName}.xml");
                    ExportToXML(chartData, filePath);
                    break;
            }

            AssetDatabase.Refresh();
            EditorUtility.DisplayDialog("Export Success", 
                $"Chart data exported to:\n{filePath}", "OK");
        }
        catch (System.Exception ex)
        {
            EditorUtility.DisplayDialog("Export Error", 
                $"Failed to export chart data:\n{ex.Message}", "OK");
        }
    }

    private void ExportAllChartData()
    {
        string[] guids = AssetDatabase.FindAssets("t:LineChartData", new[] { "Assets/Data" });
        
        if (guids.Length == 0)
        {
            EditorUtility.DisplayDialog("No Data", "No LineChartData assets found in Assets/Data folder.", "OK");
            return;
        }

        int exportedCount = 0;
        foreach (string guid in guids)
        {
            string path = AssetDatabase.GUIDToAssetPath(guid);
            LineChartData chartData = AssetDatabase.LoadAssetAtPath<LineChartData>(path);
            
            if (chartData != null)
            {
                try
                {
                    ExportChartData(chartData);
                    exportedCount++;
                }
                catch (System.Exception ex)
                {
                    Debug.LogError($"Failed to export {chartData.name}: {ex.Message}");
                }
            }
        }

        EditorUtility.DisplayDialog("Export Complete",
            $"Exported {exportedCount} chart data files to {exportFolder}", "OK");
    }

    private void ExportToJSON(LineChartData chartData, string filePath)
    {
        var jsonData = new
        {
            chartTitle = chartData.chartTitle,
            chartSubtitle = chartData.chartSubtitle,
            chartWidth = chartData.chartWidth,
            chartHeight = chartData.chartHeight,
            xAxisLabels = chartData.xAxisLabels,
            yAxisMin = chartData.yAxisMin,
            yAxisMax = chartData.yAxisMax,
            useCustomYRange = chartData.useCustomYRange,
            backgroundColor = ColorToHex(chartData.backgroundColor),
            gridColor = ColorToHex(chartData.gridColor),
            showGrid = chartData.showGrid,
            seriesData = chartData.seriesData.ConvertAll(series => new
            {
                seriesName = series.seriesName,
                lineColor = ColorToHex(series.lineColor),
                lineWidth = series.lineWidth,
                lineType = series.lineType.ToString(),
                showSymbols = series.showSymbols,
                symbolSize = series.symbolSize,
                showArea = series.showArea,
                areaColor = ColorToHex(series.areaColor),
                dataPoints = series.dataPoints,
                isVisible = series.isVisible
            })
        };

        string json = JsonUtility.ToJson(jsonData, true);
        File.WriteAllText(filePath, json);
    }

    private void ExportToCSV(LineChartData chartData, string filePath)
    {
        using (var writer = new StreamWriter(filePath))
        {
            // Write header
            writer.Write("X-Axis");
            foreach (var series in chartData.seriesData)
            {
                if (series.isVisible)
                    writer.Write($",{series.seriesName}");
            }
            writer.WriteLine();

            // Write data
            int maxDataPoints = 0;
            foreach (var series in chartData.seriesData)
            {
                if (series.isVisible && series.dataPoints.Count > maxDataPoints)
                    maxDataPoints = series.dataPoints.Count;
            }

            for (int i = 0; i < maxDataPoints; i++)
            {
                // X-axis label or index
                if (i < chartData.xAxisLabels.Count)
                    writer.Write(chartData.xAxisLabels[i]);
                else
                    writer.Write(i.ToString());

                // Data points for each series
                foreach (var series in chartData.seriesData)
                {
                    if (series.isVisible)
                    {
                        if (i < series.dataPoints.Count)
                            writer.Write($",{series.dataPoints[i]}");
                        else
                            writer.Write(",");
                    }
                }
                writer.WriteLine();
            }
        }
    }

    private void ExportToXML(LineChartData chartData, string filePath)
    {
        using (var writer = new StreamWriter(filePath))
        {
            writer.WriteLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
            writer.WriteLine("<LineChartData>");
            writer.WriteLine($"  <ChartTitle>{chartData.chartTitle}</ChartTitle>");
            writer.WriteLine($"  <ChartSubtitle>{chartData.chartSubtitle}</ChartSubtitle>");
            writer.WriteLine($"  <ChartWidth>{chartData.chartWidth}</ChartWidth>");
            writer.WriteLine($"  <ChartHeight>{chartData.chartHeight}</ChartHeight>");
            writer.WriteLine($"  <UseCustomYRange>{chartData.useCustomYRange}</UseCustomYRange>");
            writer.WriteLine($"  <YAxisMin>{chartData.yAxisMin}</YAxisMin>");
            writer.WriteLine($"  <YAxisMax>{chartData.yAxisMax}</YAxisMax>");
            writer.WriteLine($"  <BackgroundColor>{ColorToHex(chartData.backgroundColor)}</BackgroundColor>");
            writer.WriteLine($"  <GridColor>{ColorToHex(chartData.gridColor)}</GridColor>");
            writer.WriteLine($"  <ShowGrid>{chartData.showGrid}</ShowGrid>");

            writer.WriteLine("  <XAxisLabels>");
            foreach (var label in chartData.xAxisLabels)
            {
                writer.WriteLine($"    <Label>{label}</Label>");
            }
            writer.WriteLine("  </XAxisLabels>");

            writer.WriteLine("  <SeriesData>");
            foreach (var series in chartData.seriesData)
            {
                writer.WriteLine("    <Series>");
                writer.WriteLine($"      <Name>{series.seriesName}</Name>");
                writer.WriteLine($"      <LineColor>{ColorToHex(series.lineColor)}</LineColor>");
                writer.WriteLine($"      <LineWidth>{series.lineWidth}</LineWidth>");
                writer.WriteLine($"      <LineType>{series.lineType}</LineType>");
                writer.WriteLine($"      <ShowSymbols>{series.showSymbols}</ShowSymbols>");
                writer.WriteLine($"      <SymbolSize>{series.symbolSize}</SymbolSize>");
                writer.WriteLine($"      <ShowArea>{series.showArea}</ShowArea>");
                writer.WriteLine($"      <AreaColor>{ColorToHex(series.areaColor)}</AreaColor>");
                writer.WriteLine($"      <IsVisible>{series.isVisible}</IsVisible>");
                writer.WriteLine("      <DataPoints>");
                foreach (var point in series.dataPoints)
                {
                    writer.WriteLine($"        <Point>{point}</Point>");
                }
                writer.WriteLine("      </DataPoints>");
                writer.WriteLine("    </Series>");
            }
            writer.WriteLine("  </SeriesData>");
            writer.WriteLine("</LineChartData>");
        }
    }

    private string ColorToHex(Color color)
    {
        return $"#{ColorUtility.ToHtmlStringRGBA(color)}";
    }

    #endregion

    #region Preview Methods

    private void PreviewChart(LineChartData chartData)
    {
        // Placeholder for chart preview functionality
        // This could integrate with XCharts or other charting libraries
        EditorUtility.DisplayDialog("Preview Chart",
            $"Chart Preview for '{chartData.chartTitle}'\n\n" +
            $"Series Count: {chartData.seriesData.Count}\n" +
            $"Data Points: {(chartData.seriesData.Count > 0 ? chartData.seriesData[0].dataPoints.Count.ToString() : "0")}\n" +
            $"Chart Size: {chartData.chartWidth}x{chartData.chartHeight}\n\n" +
            "Note: Full chart rendering integration can be added here.", "OK");
    }

    #endregion

    /// <summary>
    /// Class for creating new LineChartData assets
    /// </summary>
    public class CreateNewLineChartData
    {
        public CreateNewLineChartData()
        {
            chartData = ScriptableObject.CreateInstance<LineChartData>();
            chartData.name = "New Line Chart Data";
            chartData.chartTitle = "New Line Chart";
        }

        [InlineEditor(ObjectFieldMode = InlineEditorObjectFieldModes.Hidden)]
        public LineChartData chartData;

        [Button("Create New Chart Data")]
        private void CreateChartData()
        {
            string fileName = string.IsNullOrEmpty(chartData.chartTitle) ? "New Line Chart Data" : chartData.chartTitle;
            // Clean up the name for file system
            fileName = System.Text.RegularExpressions.Regex.Replace(fileName, @"[^\w\d\s]", "_");

            AssetDatabase.CreateAsset(chartData, $"Assets/Data/{fileName}.asset");
            AssetDatabase.SaveAssets();

            chartData = ScriptableObject.CreateInstance<LineChartData>();
            chartData.chartTitle = "New Line Chart";
        }
    }

    /// <summary>
    /// Settings window for LineChartDataEditor configuration
    /// </summary>
    public class LineChartSettingsWindow : EditorWindow
    {
        public static void ShowWindow()
        {
            var window = GetWindow<LineChartSettingsWindow>("Chart Editor Settings");
            window.minSize = new Vector2(400, 300);
            window.Show();
        }

        private void OnGUI()
        {
            GUILayout.Label("Line Chart Editor Settings", EditorStyles.boldLabel);
            EditorGUILayout.Space();

            // Export folder field
            EditorGUILayout.BeginHorizontal();
            exportFolder = EditorGUILayout.TextField("Export Folder", exportFolder);
            if (GUILayout.Button("Browse", GUILayout.Width(60)))
            {
                string selectedFolder = EditorUtility.OpenFolderPanel("Select Export Folder", exportFolder, "");
                if (!string.IsNullOrEmpty(selectedFolder))
                {
                    // Convert absolute path to relative path if within project
                    if (selectedFolder.StartsWith(Application.dataPath))
                    {
                        exportFolder = "Assets" + selectedFolder.Substring(Application.dataPath.Length);
                    }
                    else
                    {
                        exportFolder = selectedFolder;
                    }
                }
            }
            EditorGUILayout.EndHorizontal();

            // Export format dropdown
            exportFormat = (ExportFormat)EditorGUILayout.EnumPopup("Export Format", exportFormat);

            // Auto-validate option
            autoValidate = EditorGUILayout.Toggle("Auto Validate", autoValidate);

            EditorGUILayout.Space();

            // Help text
            EditorGUILayout.HelpBox(
                "Export Folder: Where exported chart data files will be saved.\n" +
                "Export Format: File format for exported data (JSON, CSV, or XML).\n" +
                "Auto Validate: Automatically show validation status for selected charts.",
                MessageType.Info);

            EditorGUILayout.Space();

            // Save settings button
            if (GUILayout.Button("Save Settings"))
            {
                var editor = GetWindow<LineChartDataEditor>();
                if (editor != null)
                {
                    editor.SaveSettings();
                }
                EditorUtility.DisplayDialog("Settings Saved", "Chart editor settings have been saved.", "OK");
            }

            EditorGUILayout.Space();

            // Create export folder button
            if (GUILayout.Button("Create Export Folder"))
            {
                if (!Directory.Exists(exportFolder))
                {
                    Directory.CreateDirectory(exportFolder);
                    AssetDatabase.Refresh();
                    EditorUtility.DisplayDialog("Folder Created", $"Export folder created at:\n{exportFolder}", "OK");
                }
                else
                {
                    EditorUtility.DisplayDialog("Folder Exists", $"Export folder already exists at:\n{exportFolder}", "OK");
                }
            }

            // Reset to defaults button
            if (GUILayout.Button("Reset to Defaults"))
            {
                if (EditorUtility.DisplayDialog("Reset Settings",
                    "Are you sure you want to reset all settings to defaults?", "Reset", "Cancel"))
                {
                    exportFolder = "Assets/Exports/ChartData";
                    exportFormat = ExportFormat.JSON;
                    autoValidate = true;
                }
            }
        }
    }
}
