using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class ClickableAIObject : MonoBehaviour
{
    private static HashSet<int> clickedInstanceIDs = new HashSet<int>();



    public string objectDisplayName; // Optional: override default GameObject name
    private ChatGPTUI chatGPT;

    void Start()
    {
        chatGPT = FindObjectOfType<ChatGPTUI>();
    }

        void OnMouseDown()
    {
        int id = gameObject.GetInstanceID();
        if (clickedInstanceIDs.Contains(id)) return;

        clickedInstanceIDs.Add(id);

        string nameToUse = string.IsNullOrEmpty(objectDisplayName) ? gameObject.name : objectDisplayName;

        if (chatGPT != null)
        {
            chatGPT.TriggerAIResponse(nameToUse);
        }
    }
}
