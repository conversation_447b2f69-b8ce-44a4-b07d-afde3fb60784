using UnityEngine;
using Card;

public class CardSelectable : MonoBehaviour
{
    private GameCard gameCard;

    void Awake()
    {
        gameCard = GetComponent<GameCard>();
    }

    void OnMouseDown()
    {
        Debug.Log("You clicked a selectable card: " + gameCard.name);

        // Do something with the selected card
        // e.g., reward player, activate ability, etc.

        // Optionally: remove highlight and disable selection
        SpriteRenderer sr = gameCard.GetComponent<SpriteRenderer>();
        if (sr != null)
        {
            sr.color = Color.white;
        }

        Destroy(this); // Remove selectable status
    }
}
