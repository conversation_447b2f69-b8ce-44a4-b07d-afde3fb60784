using System.Collections;
using System.Collections.Generic;
using System.Security.Claims;
using Sirenix.OdinInspector;
using TMPro;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.UIElements;

namespace Card
{
    public class GameCard : MonoBehaviour
    {
        [InlineEditor] public CardData cardData;
        
        public int value = 0; //default value
        TextMeshProUGUI titleText, detailedText, valueText;
        Zoom gameCam;

        Collider2D cld;
        Rigidbody2D rb;
        SpriteRenderer spr;
        [HideInInspector] public bool isStack, simulated;
        [HideInInspector] public static bool mouseUp, mouseHold;
        [HideInInspector] public Vector3 startPos, parentOrigin;
        public GameObject child, parent;
        [HideInInspector] public GameCard parentCard, childCard;

        // 碰撞器大小相关变量
        private Vector2 initialColliderSize; // 初始碰撞器大小
        private Vector2 initialColliderOffset; // 初始碰撞器偏移
        [SerializeField] private float stackedColliderHeight = 0.89f; // 堆叠时碰撞器高度，可在Inspector中调整

        [Header("Idea Product")]
        public List<GameObject> materials = new List<GameObject>();
        public List<int> matchingNum = new List<int>();
        public int materialSize = 0;
        public float requireTime;

        public bool firstCheck;

        Vector3 offSet;

        //public STATE currentState;

        /*
        public enum STATE
        {
            NoCard,
            CardDrag,
            CardRelease
        }
        */
        private void Awake()
        {
            // Only set default position if startPos hasn't been set yet
            if (startPos == Vector3.zero)
            {
                // Use current position as the start position instead of a fixed default
                startPos = transform.position;
            }

            // Get required components from this GameObject
            cld = GetComponent<Collider2D>();
            spr = GetComponent<SpriteRenderer>();
            rb = GetComponent<Rigidbody2D>();

            // 保存初始碰撞器大小和偏移
            if (cld != null && cld is BoxCollider2D)
            {
                BoxCollider2D boxCollider = (BoxCollider2D)cld;
                initialColliderSize = boxCollider.size;
                initialColliderOffset = boxCollider.offset;
            }

            // Find camera with null check
            GameObject mainCamera = GameObject.Find("Main Camera");
            if (mainCamera != null)
            {
                gameCam = mainCamera.GetComponent<Zoom>();
            }

            // Find UI elements with null checks
            GameObject titleTextObj = GameObject.Find("TitleText (TMP)");
            if (titleTextObj != null)
            {
                titleText = titleTextObj.GetComponent<TextMeshProUGUI>();
            }

            GameObject detailedTextObj = GameObject.Find("DetailedText (TMP)");
            if (detailedTextObj != null)
            {
                detailedText = detailedTextObj.GetComponent<TextMeshProUGUI>();
            }

            GameObject valueTextObj = GameObject.Find("ValueText (TMP)");
            if (valueTextObj != null)
            {
                valueText = valueTextObj.GetComponent<TextMeshProUGUI>();
            }
        }
        private void Start()
        {
            firstCheck = false;
            rb.mass = 0.4f;
            //currentState = STATE.NoCard;
            parent = null;
            child = null;
            //isColliding = false;
            cld.enabled = false;
            //cld.isTrigger = true;

            // Only lerp to startPos if the card is not already at its start position
            // This prevents all cards from moving to the same position
            if (Vector3.Distance(transform.position, startPos) > 0.1f)
            {
                StartCoroutine(lerpCard(gameObject, startPos));
            }
            else
            {
                // If we're already at the start position, just set default properties
                SetDefault();
            }

            ItemsUpdate();
        }

        private void Update()
        {
            // Check if GameManager exists
            if (GameManager.instance != null)
            {
                if (GameManager.instance.currentCard == gameObject)
                {
                    simulated = true;
                }
                else
                {
                    simulated = false;
                }
            }

            // Check if collider exists and is enabled
            if (cld != null && cld.enabled)
            {
                FollowParent();
            }

            if (child == null && parent == null)
            {
                isStack = false;
            }

            ColliderChange();
        }

        void ColliderChange()
        {
            // 确保cld是BoxCollider2D类型
            if (cld == null || !(cld is BoxCollider2D))
                return;

            BoxCollider2D boxCollider = (BoxCollider2D)cld;

            if (child != null)
            {
                // 有子卡牌时，使用可配置的堆叠高度，保持宽度不变
                boxCollider.size = new Vector2(initialColliderSize.x, stackedColliderHeight);

                // 计算新的偏移，使碰撞器位于卡牌顶部
                float offsetY = (initialColliderSize.y - stackedColliderHeight) / 2;
                boxCollider.offset = new Vector2(initialColliderOffset.x, offsetY);
            }
            else
            {
                // 没有子卡牌时，恢复初始大小和偏移
                boxCollider.size = initialColliderSize;
                boxCollider.offset = initialColliderOffset;
            }
        }

        void ItemsUpdate()
        {
            // Only update GameManager if it exists
            if (GameManager.instance == null)
                return;

            if (CompareTag("Coin"))
            {
                GameManager.instance.coinNum++;
                GameManager.instance.CoinUpdate();
            }
            else if (CompareTag("Villager"))
            {
                GameManager.instance.people.Add(gameObject);
                GameManager.instance.FoodUpdate();
                GameManager.instance.cardNum++;
                GameManager.instance.StorageUpdate();
            }
            else
            {
                GameManager.instance.cardNum++;
                GameManager.instance.StorageUpdate();
            }
        }

        private void OnMouseDown()
        {
            Vector3 mousePos = Camera.main.ScreenToWorldPoint(Input.mousePosition);
            offSet = transform.position - mousePos;

            // Play sound if SoundManager exists
            if (SoundManager.instance != null)
            {
                if (child == null && parent == null)
                {
                    if (gameObject.CompareTag("Villager"))
                    {
                        SoundManager.instance.PlayVillagerCard();
                    }
                    else if (gameObject.CompareTag("Coin"))
                    {
                        SoundManager.instance.PlayCoinCard();
                    }
                    else
                    {
                        SoundManager.instance.PlayCardPickUp();
                    }
                }
                else
                {
                    SoundManager.instance.PlaystackCard();
                }
            }
            ColliderChange();
        }
        
        private void OnMouseDrag()
        {
            firstCheck = true;

            // Disable camera drag if gameCam exists
            if (gameCam != null)
            {
                gameCam.enableDrag = false;
            }

            //currentState = STATE.CardDrag;
            Vector3 mousePos = Camera.main.ScreenToWorldPoint(Input.mousePosition);
            //transform.position = new Vector3(mousePos.x, mousePos.y, 0);
            transform.position = new Vector3(mousePos.x + offSet.x, mousePos.y + offSet.y, 0);

            // Set to highest sorting order to ensure this card is always on top
            SetToHighestSortingOrder();

            // Update GameManager if it exists
            if (GameManager.instance != null)
            {
                GameManager.instance.currentCard = gameObject;
            }

            mouseHold = true;
            mouseUp = false;
            //ChildFollow();
            if (parent != null && parentCard != null)
            {
                parentCard.child = null;
                parentCard.childCard = null;
                parent = null;
            }
            //cld.isTrigger = true;
        }

        private void OnMouseOver()
        {
            // Only update UI text if the text components exist
            if (titleText == null || detailedText == null || valueText == null)
                return;

            if (gameObject.name == "Coin(Clone)")
            {
                titleText.text = "COIN";
                detailedText.text = "Humanity's best friend";
                valueText.text = "Can't be sold";
            }

            if (gameObject.name == "Villager(Clone)")
            {
                titleText.text = "VILLAGER";
                detailedText.text = "A hard-working pioneer";
                valueText.text = "Can't be sold";
            }

            if (gameObject.name == "Berry_Bush(Clone)")
            {
                titleText.text = "BERRY BUSH";
                detailedText.text = "A bush with delicious berries it";
                valueText.text = "1";
            }

            if (gameObject.name == "Rock(Clone)")
            {
                titleText.text = "ROCK";
                detailedText.text = "It looks sturdy, yet punchable";
                valueText.text = "";
            }

            if (gameObject.name == "Wood(Clone)")
            {
                titleText.text = "WOOD";
                detailedText.text = "A simple resource";
                valueText.text = "1";
            }
        }

        private void OnMouseExit()
        {
            // Only update UI text if the text components exist
            if (titleText != null)
                titleText.text = "";

            if (detailedText != null)
                detailedText.text = "";

            if (valueText != null)
                valueText.text = "";
        }


        private void OnMouseUp()
        {
            //cld.isTrigger = false;

            // Keep the same sorting order that was set during dragging (highest + 1)
            // No need to change it - it's already at the highest position

            // Play sound if SoundManager exists
            if (SoundManager.instance != null)
            {
                if (gameObject.CompareTag("Villager"))
                {
                    SoundManager.instance.PlayVillagerCard();
                }
                else if (gameObject.CompareTag("Coin"))
                {
                    SoundManager.instance.PlayCoinCard();
                }
                else
                {
                    SoundManager.instance.PlayCardDrop();
                }
            }

            mouseUp = true;
            mouseHold = false;
            SortLayer();

            // Enable camera drag if gameCam exists
            if (gameCam != null)
            {
                gameCam.enableDrag = true;
            }
        }
        void FollowParent()
        {
            if (parent != null)
            {
                if (parentOrigin != parent.transform.position)
                {
                    Vector3 newPosition = parent.transform.position + Vector3.down * 0.3f;
                    transform.position = newPosition;
                    parentOrigin = parent.transform.position;
                }
                
                //childCard.ChildFollow();

                /*
                if (transform.position != originalPos)
                {
                    StartCoroutine(lerpCard(child, transform.position + Vector3.down * 0.3f));
                    childCard.ChildFollow();
                }
                */
            }

            if (child != null)
            {
                SortLayer();
            }
        }

        void SortLayer()
        {
            GameCard tempChild = childCard;
            int lastChildSortingOrder = spr.sortingOrder + 1; // Initialize with parent's sorting order + 1

            while (tempChild != null)
            {
                tempChild.spr.sortingOrder = lastChildSortingOrder + 1;

                // Update lastChildSortingOrder for the next child in the chain
                lastChildSortingOrder = tempChild.spr.sortingOrder;

                tempChild = tempChild.childCard;
            }
        }

        /// <summary>
        /// Set this card to the highest sorting order among all cards + 1
        /// </summary>
        void SetToHighestSortingOrder()
        {
            int highestOrder = GetHighestSortingOrder();
            spr.sortingOrder = highestOrder + 1;
        }

        /// <summary>
        /// Find the highest sorting order among all GameCards in the scene
        /// </summary>
        int GetHighestSortingOrder()
        {
            int highest = 0;

            // Find all GameCard objects in the scene
            GameCard[] allCards = FindObjectsOfType<GameCard>();

            foreach (GameCard card in allCards)
            {
                if (card != this && card.spr != null)
                {
                    highest = Mathf.Max(highest, card.spr.sortingOrder);
                }
            }

            return highest;
        }

        public IEnumerator lerpCard(GameObject card, Vector3 targetPos)
        {
            while (Vector3.Distance(card.transform.position, targetPos) > 0.01f)
            {
                yield return new WaitForSeconds(0.01f);
                card.transform.position = Vector3.Lerp(card.transform.position, targetPos, 0.1f);
            }
            if (Vector3.Distance(card.transform.position, targetPos) <= 0.01f)
            {
                card.transform.position = targetPos;
                StopCoroutine(lerpCard(card, startPos));
                SetDefault();
            }
        }

        void SetDefault()
        {
            //cld.isTrigger = false;
            cld.enabled = true;
            rb.drag = 8;
            cld.isTrigger = true;
        }

        public void CardDestroy()
        {
            if (CompareTag("Coin"))
            {
                GameManager.instance.coinNum--;
                GameManager.instance.CoinUpdate();
            }
            else if (CompareTag("Villager"))
            {
                GameManager.instance.people.Remove(gameObject);
                GameManager.instance.FoodUpdate();
                GameManager.instance.cardNum--;
                GameManager.instance.StorageUpdate();
            }
            else if (CompareTag("Food"))
            {
                GameManager.instance.foodNum--;
                GameManager.instance.foods.Remove(gameObject);
                GameManager.instance.FoodUpdate();
                GameManager.instance.cardNum--;
                GameManager.instance.StorageUpdate();
            }
            else
            {
                GameManager.instance.cardNum--;
                GameManager.instance.StorageUpdate();
            }
            Destroy(gameObject);
        }
    }
}