using UnityEngine;
using System.Linq;
using System.Collections.Generic;
using Card;


public class CardSelector : MonoBehaviour
{
    private List<GameCard> selectedCards = new List<GameCard>();

    private bool hasTriggered = false;

    void Update()
    {
        if (hasTriggered) return;

        GameCard[] allCards = FindObjectsOfType<GameCard>();
        if (allCards.Length == 5)
        {
            hasTriggered = true;
            SelectThreeRandomCards();
        }
    }
        
    

    public void SelectThreeRandomCards()
    {
        GameCard[] allCards = FindObjectsOfType<GameCard>();

        selectedCards = allCards
            .OrderBy(card => Random.value)
            .Take(3)
            .ToList();

        Debug.Log("🎴 Player must choose one of the following cards:");
        foreach (GameCard card in selectedCards)
        {
            Debug.Log($"- {card.name} (value: {card.value})");
            HighlightCard(card);
        }
    }

    void HighlightCard(GameCard card)
    {
        // Example: change card color to indicate it's selectable
        SpriteRenderer sr = card.GetComponent<SpriteRenderer>();
        if (sr != null)
        {
            sr.color = Color.green;
        }

        // You can also add a "selectable" flag to the card
        card.gameObject.AddComponent<CardSelectable>();
    }
}
