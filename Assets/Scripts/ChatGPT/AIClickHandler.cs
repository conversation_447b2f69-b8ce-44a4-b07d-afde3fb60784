using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class AIClickHandler : MonoBehaviour
{
   

    public List<string> targetTags = new List<string> { "Resources", "Villager", "Human","Structure" };
    private HashSet<string> clickedObjects = new HashSet<string>();
    private ChatGPTUI chatGPT;


    void Start()
    {
        chatGPT = GetComponent<ChatGPTUI>(); // assumes same object
    }

    void Update()
    {
        // Ignore if clicking UI
        if (EventSystem.current != null && EventSystem.current.IsPointerOverGameObject())
            return;

        if (Input.GetMouseButtonDown(0))
        {
            Vector2 mousePos = Camera.main.ScreenToWorldPoint(Input.mousePosition);
            RaycastHit2D hit = Physics2D.Raycast(mousePos, Vector2.zero);

            if (hit.collider != null)
            {
                Debug.DrawRay(mousePos, Vector2.up * 0.01f, Color.red, 1f);

                GameObject clicked = hit.collider.gameObject;
                if (targetTags.Contains(clicked.tag) && !clickedObjects.Contains(clicked.name))
                {
                    clickedObjects.Add(clicked.name);
                    chatGPT.TriggerAIResponse(clicked.name);
                }
            }
        }
    }
}
