using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Networking;
using System.Collections;
using System.Text;
using System;
using System.IO;

[System.Serializable]
public class ChatMessage
{
    public string role;
    public string content;
}

[System.Serializable]
public class ChatRequest
{
    public string model;
    public ChatMessage[] messages;
}

[System.Serializable]
public class ChatResponse
{
    public Choice[] choices;
    
    [System.Serializable]
    public class Choice
    {
        public ChatMessage message;
    }
}

public class ChatGPTUI : MonoBehaviour
{
    public InputField userInput;
    public Button sendButton;
    public Text chatOutput;
    public ScrollRect scrollRect;
    

    [TextArea] //public string systemPrompt = "你是一只可爱的猫娘，用萌萌的语气和主人对话，经常使用'喵~'和'主人'";
    string path = Application.streamingAssetsPath + "/style.txt";
    string stylePrompt;

    // 从安全位置加载API密钥
    private string apiKey => LoadApiKey();
    public string model = "gpt-3.5-turbo";
    private bool isWaitingForResponse = false;

    void Start()
    {
        sendButton.onClick.AddListener(OnSend);
        stylePrompt = File.ReadAllText(path);
        StartCoroutine(TestConnection());
    }
    

    string LoadApiKey()
    {
        // 实际项目中应该从安全位置加载API密钥
        // 例如：PlayerPrefs、加密文件或服务器获取
        return "sk-CJPDzetfzqeHTCoRzsLT53rwsFpDPIFBCTQf7Bx2t8rGZnl2"; // 返回空字符串，实际使用时需要替换
    }

    void OnSend()
    {
        if (!isWaitingForResponse && !string.IsNullOrWhiteSpace(userInput.text))
        {
            string userMessage = userInput.text;
            AppendToChat("You: " + userMessage);
            userInput.text = "";
            isWaitingForResponse = true;
            sendButton.interactable = false;
            StartCoroutine(SendToDeepSeek(userMessage));
        }
    }

    void AppendToChat(string message)
    {
        chatOutput.text += message + "\n\n";
        Canvas.ForceUpdateCanvases();
        scrollRect.verticalNormalizedPosition = 0f;
        
    }

    IEnumerator SendToDeepSeek(string userMessage)
    {
    string url = "https://api.chatanywhere.tech/v1/chat/completions";  // ✅ Use ChatAnywhere endpoint

    ChatRequest requestData = new ChatRequest
    {
        model = model,
        messages = new ChatMessage[]
        {
            new ChatMessage { role = "system", content = stylePrompt },
            new ChatMessage { role = "user", content = userMessage }
        }
    };

    string requestJson = JsonUtility.ToJson(requestData);
    byte[] body = Encoding.UTF8.GetBytes(requestJson);

    using (UnityWebRequest request = new UnityWebRequest(url, "POST"))
    {
        request.uploadHandler = new UploadHandlerRaw(body);
        request.downloadHandler = new DownloadHandlerBuffer();
        request.SetRequestHeader("Content-Type", "application/json");
        request.SetRequestHeader("Authorization", "Bearer " + apiKey);  // ✅ Use your ChatAnywhere API key

        yield return request.SendWebRequest();

        isWaitingForResponse = false;
        sendButton.interactable = true;

        if (request.result != UnityWebRequest.Result.Success)
        {
            Debug.LogError("Error: " + request.error);
            Debug.Log("Status Code: " + request.responseCode);      
            Debug.Log("Response Body: " + request.downloadHandler.text);
            AppendToChat("猫娘暂时无法回应喵~ (错误: " + request.error + ")");
        }
        else
        {
            try
            {
                ChatResponse response = JsonUtility.FromJson<ChatResponse>(request.downloadHandler.text);
                if (response.choices != null && response.choices.Length > 0)
                {
                    string reply = response.choices[0].message.content;
                    AppendToChat(reply);
                }
                else
                {
                    AppendToChat("喵~ 猫娘不知道怎么回应了");
                }
            }
            catch (Exception e)
            {
                Debug.LogError("JSON解析错误: " + e.Message);
                AppendToChat("猫娘的回答有点奇怪喵~ (解析错误)");
            }
        }
    }
    }
    IEnumerator TestConnection()
{
    string testUrl = "https://api.chatanywhere.tech/v1/models";
    using (UnityWebRequest test = UnityWebRequest.Get(testUrl))
    {
        test.SetRequestHeader("Authorization", "Bearer " + apiKey);
        test.timeout = 10;
        
        yield return test.SendWebRequest();
        
        if (test.result == UnityWebRequest.Result.ConnectionError)
        {
            Debug.LogError("网络连接错误: " + test.error);
            AppendToChat("无法连接到猫娘服务器喵~ 请检查网络设置");
        }
        else if (test.result == UnityWebRequest.Result.ProtocolError)
        {
            Debug.LogError("API协议错误: " + test.error);
            Debug.Log("HTTP状态码: " + test.responseCode);
            AppendToChat("猫娘服务器有点问题喵~ (HTTP " + test.responseCode + ")");
        }
        else if (test.result == UnityWebRequest.Result.Success)
        {
            Debug.Log("连接测试成功!");
            Debug.Log("响应: " + test.downloadHandler.text);
        }
    }
}

void Update()
{
    if (userInput.isFocused && Input.GetKeyDown(KeyCode.Return))
    {
        sendButton.onClick.Invoke(); 
    }
    
}

public void TriggerAIResponse(string objectName)
{
    string prompt = $"你看到了一个叫“{objectName}”的物体，简单评价一下它。";
    AppendToChat("You clicked: " + objectName);
    StartCoroutine(SendToDeepSeek(prompt));
}



}
